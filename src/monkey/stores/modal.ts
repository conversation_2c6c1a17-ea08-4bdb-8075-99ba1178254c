import type { ModalState } from './types';

/**
 * 认证模态框状态管理
 */
export const useAuthModalStore = defineStore('authModal', () => {
  const state = reactive<ModalState>({
    visible: false,
  });

  /**
   * 打开登录弹窗
   */
  function open(): void {
    state.visible = true;
  }

  /**
   * 关闭登录弹窗
   */
  function close(): void {
    state.visible = false;
  }

  return {
    ...toRefs(state),
    open,
    close,
  };
}); 