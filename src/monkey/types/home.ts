export interface BannerData {
  id: string;
  tpzt: string;
  tpzt_key: string;
  sftz: string;
  sftz_key: string;
  tpmc: string;
  zsnr: string;
  px: string;
  tp: Array<{
    refId: string;
  }>;
  creater: Array<{
    id: string;
    name: string;
  }>;
}

export interface BannerItem {
  id: string;
  url?: string;
  name: string;
  isLink?: string;
  zsnr?: string;
  createTime?: string;
}

export interface InboundDateItem {
  week: string;
  date: string;
  dateStr?: string;
}
