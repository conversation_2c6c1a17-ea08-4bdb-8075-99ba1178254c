<template>
  <ly-layout>
    <div class="px-4 bg-white">
      <uni-search-bar v-model="searchValue" placeholder="请输入药材名称" @confirm="handleSearch" @clear="handleClear" cancelButton="none" />
    </div>
    <div class="px-4 pt-2 pr-5">
      <van-index-bar :index-list="indexList">
        <view v-for="item in listLetter" :key="item.letter">
          <van-index-anchor :index="item.letter" />
          <div class="grid grid-cols-4 gap-3 my-2">
            <div v-for="item in item.data" :key="item.id" @click="handleItemClick(item)" class="bg-white rounded-lg p-3 shadow-sm active:bg-green-200/30 flex flex-col h-16">
              <div class="text-sm font-medium text-gray-800">{{ item.bzmc }}</div>
              <div class="text-xs text-gray-500 mt-1 truncate" v-if="item.pinyin">{{ item.pinyin }}</div>
            </div>
          </div>
        </view>
      </van-index-bar>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import { MedicinalItem } from '@/monkey/types';

  const list = ref<MedicinalItem[]>([]);

  // 药材列表
  const listLetter = ref<MedicinalItem[]>([]);

  // 索引列表
  const indexList = ref<string[]>([]);

  // 搜索值
  const searchValue = ref<string>('');

  /**
   * 获取药材列表
   */
  const getMedicinalItemList = async () => {
    try {
      const { errcode, data } = await monkey.$api.medicine.getMedicinalItemList();
      if (errcode == 0) {
        console.log('🚀 ~ getMedicinalItemList ~ data:', data);
        // 将数据转换为MedicinalItem类型
        list.value = data;
        // 将数据转换为索引列表
        listLetter.value = createLetterList(data);
      }
    } catch (error) {
      console.log(error);
    }
  };

  /**
   * 创建索引列表
   * @param data 药材列表
   * @returns
   */
  const createLetterList = (data: MedicinalItem[]) => {
    // 按首字母分组
    const groupedData = {};

    data.forEach((item) => {
      // 从ywsc字段中提取拼音缩写
      const ywscParts = item.ywsc.split(',');

      // 尝试找到拼音缩写（通常是纯小写字母的部分）
      let pinyin = '';
      for (const part of ywscParts) {
        // 检查是否是拼音缩写（纯小写字母）
        if (/^[a-z]+$/.test(part.trim())) {
          pinyin = part.trim();
          break;
        }
      }

      // 如果没找到拼音，使用药材名称的首字母
      const firstLetter = pinyin.charAt(0).toUpperCase() || item.bzmc.charAt(0).toUpperCase();

      // 如果该首字母组不存在，则创建
      if (!groupedData[firstLetter]) {
        groupedData[firstLetter] = [];
      }

      // 将药材添加到对应首字母组，并添加提取的拼音
      const enrichedItem = {
        ...item,
        pinyin: pinyin, // 添加提取出的拼音
      };

      groupedData[firstLetter].push(enrichedItem);
    });

    // 转换为list格式
    const formattedList = Object.keys(groupedData)
      .sort()
      .map((letter) => ({
        letter,
        data: groupedData[letter],
      }));

    indexList.value = formattedList.map((item) => item.letter);
    return formattedList;
  };

  /**
   * 过滤列表
   * @param list 药材列表
   * @returns
   */
  const filterList = (list: MedicinalItem[]) => {
    return list.filter((item) => item.bzmc.includes(searchValue.value));
  };

  /**
   * 搜索
   */
  const handleSearch = () => {
    console.log('🚀 ~ handleSearch ~ searchValue:', searchValue.value);
    const result = filterList(list.value);
    if (result.length > 0) {
      // 过滤列表
      listLetter.value = createLetterList(result);
    } else {
      monkey.$helper.toast.warning('暂无数据');
    }
  };

  /**
   * 清空搜索
   */
  const handleClear = () => {
    listLetter.value = createLetterList(list.value);
  };

  /**
   * 点击药材项
   * @param item 药材项
   */
  const handleItemClick = (item: MedicinalItem) => {
    monkey.$router.navigateTo(`/modules/home/<USER>/detail?ycbm=${item.ycbm}`);
  };

  onLoad(() => {
    getMedicinalItemList();
  });
</script>

<style lang="scss" scoped>
</style>
