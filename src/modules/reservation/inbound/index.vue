<template>
  <ly-layout>
    <div class="px-2 py-4 bg-white">
      <uni-steps :options="steps" :active="active" />
    </div>
    <div class="flex-1 pt-4 box-border">
      <swiper class="size-full" :current="active" @change="handleChange" :disable-touch="true">
        <!-- 预约时间 -->
        <swiper-item class="size-full">
          <view class="p-4 bg-white mb-4">
            <div class="mb-4 flex items-center justify-between">
              <div class="mb-4">
                <uni-section title="选择预约时间" type="line" padding="0"></uni-section>
              </div>
              <div v-if="selectedDate" class="text-sm text-gray-500">{{ selectedDate }}</div>
            </div>
            <!-- 日期选择横向滚动 -->
            <scroll-view scroll-x>
              <Dates :currentDates="currentDates.slice(0, 4)" :selectedDateIndex="selectedDateIndex" @selectDate="handleSelectDate" @more="handleMoreDates" />
            </scroll-view>
          </view>
          <!-- 显示时间段上午 -->
          <view class="p-4 bg-white mb-4">
            <div class="mb-4">
              <uni-section title="上午 选择时间段" type="line" padding="0"></uni-section>
            </div>
            <view class="grid grid-cols-3 gap-2">
              <view v-for="(item, index) in selectTimesList.sw" :key="index">
                <view
                  @click="handleSelectTimeAM(item, index)"
                  :class="selectedTimeAMIndex === index ? 'bg-theme-blue text-white' : 'bg-gray-100'"
                  class="px-3.5 py-2 text-sm text-gray-500 transition-all duration-300 box-border border border-gray-200 rounded-lg flex items-center justify-center"
                >
                  {{ item.time }}
                </view>
              </view>
            </view>
          </view>
          <!-- 显示时间段下午 -->
          <view class="p-4 bg-white">
            <div class="mb-4">
              <uni-section title="下午 选择时间段" type="line" padding="0"></uni-section>
            </div>
            <view class="grid grid-cols-3 gap-2">
              <view v-for="(item, index) in selectTimesList.xw" :key="index">
                <view
                  @click="handleSelectTimePM(item, index)"
                  :class="selectedTimePMIndex === index ? 'bg-theme-blue text-white' : 'bg-gray-100'"
                  class="px-3.5 py-2 text-sm text-gray-500 transition-all duration-300 box-border border border-gray-200 rounded-lg flex items-center justify-center"
                >
                  {{ item.time }}
                </view>
              </view>
            </view>
          </view>
        </swiper-item>
        <!-- 物品选择 -->
        <swiper-item class="size-full">
          <view class="px-4 size-full">
            <Goods @update="handleConfirmGoods" />
          </view>
        </swiper-item>
        <!-- 预约信息 -->
        <swiper-item class="size-full">
          <view class="px-4 size-full">
            <Info ref="infoRef" />
          </view>
        </swiper-item>
      </swiper>
   
    </div>
    <ly-fixed-btns type="primary" @submit="handleSubmit(1)" icon="" rightIcon="i-mdi-arrow-right" text="下一步" v-if="active === 0"></ly-fixed-btns>
    <ly-fixed-btns v-if="active === 1" :fixed="false" :buttons="{
      left: {
        text: '上一步',
        icon: 'i-mdi-arrow-left',
        bgColor: 'bg-slate-500',
        click: () => handleSubmit(0)
      },
      right: {
        text: '确认物品',
        icon: 'i-mdi-check',
        bgColor: 'bg-theme-blue',
        click: () => handleSubmit(2)
      }
    }">
      <!-- <div class="grid grid-cols-2 gap-2">
        <div class="w-full text-sm bg-slate-500 text-white rounded-full h-44 flex items-center tracking-widest gap-2 justify-center active:bg-slate-600 transition-all duration-300" @click="handleSubmit(0)">
          <text class="text-base i-mdi-arrow-left"></text>
          <text class="text-sm">上一步</text>
        </div>
        <div class="w-full text-sm bg-theme-blue text-white rounded-full h-44 flex items-center tracking-widest gap-2 justify-center active:bg-blue-700 transition-all duration-300" @click="handleSubmit(2)">
          <text class="text-base i-mdi-check"></text>
          <text class="text-sm">确认物品</text>
        </div>
      </div> -->
    </ly-fixed-btns>
    <ly-fixed-btns v-if="active === 2" :fixed="false" :buttons="{
      left: {
        text: '上一步',
        icon: 'i-mdi-arrow-left',
        bgColor: 'bg-slate-500',
        click: () => handleSubmit(1)
      },
      right: {
        text: '确认信息',
        icon: 'i-mdi-check',
        bgColor: 'bg-theme-blue',
        click: () => handleSubmit(3)
      }
    }">
      <!-- <div class="grid grid-cols-2 gap-2">
        <div class="w-full text-sm bg-slate-500 text-white rounded-full h-44 flex items-center tracking-widest gap-2 justify-center active:bg-slate-600 transition-all duration-300" @click="handleSubmit(1)">
          <text class="text-base i-mdi-arrow-left"></text>
          <text class="text-sm">上一步</text>
        </div>
        <div class="w-full text-sm bg-theme-blue text-white rounded-full h-44 flex items-center tracking-widest gap-2 justify-center active:bg-blue-700 transition-all duration-300" @click="handleSubmit(3)">
          <text class="text-base i-mdi-check"></text>
          <text class="text-sm">确认信息</text>
        </div>
      </div> -->
    </ly-fixed-btns>
    <ui-popup v-model:show="showMoreDates" title="选择预约时间" position="bottom">
      <div class="p-4 box-border">
        <Dates :isMore="false" cols="4" gap="3" :currentDates="currentDates" :selectedDateIndex="selectedDateIndex" @selectDate="handleMoreSelectDate" />
      </div>
    </ui-popup>
  </ly-layout>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  import Dates from './components/dates.vue';
  import Goods from './components/goods.vue';
  import Info from './components/info.vue';
  import { HomeTypes, MedicineTypes } from '@/monkey/types';

  const { user: userInfo, hasLogin } = storeToRefs(monkey.$stores.useUserStore());

  const active = ref(2);

  // 新增：选中的日期索引
  const selectedDateIndex = ref(null);

  // 新增：选中的日期
  const selectedDate = ref<string | null>(null);

  // 新增：选中的时间段
  const selectedTimeSlot = ref<string | null>('');

  // 新增：选中的时间段索引
  const selectedTimePMIndex = ref(null);

  // 新增：选中的时间段索引
  const selectedTimeAMIndex = ref(null);

  // 新增：选中的时间段列表
  const selectTimesList = ref<any[]>([]);

  // 显示更多日期弹窗
  const showMoreDates = ref<boolean>(false);

  // 新增：选中的物品列表
  const selectedGoodsList = ref<MedicineTypes.MedicinalItem[]>([]);

  const infoRef = ref<any>(null);

  const steps = ref([
    {
      title: '预约时间',
    },
    {
      title: '物品选择',
    },
    {
      title: '预约信息',
    },
    {
      title: '订单确认',
    },
  ]);

  const currentDates = ref<HomeTypes.InboundDateItem[]>([
    { week: '周四', date: '07-24', dateStr: '2025-07-24' },
    { week: '周五', date: '07-25', dateStr: '2025-07-25' },
    { week: '周六', date: '07-26', dateStr: '2025-07-26' },
    { week: '周日', date: '07-27', dateStr: '2025-07-27' },
    { week: '周一', date: '07-28', dateStr: '2025-07-28' },
    { week: '周二', date: '07-29', dateStr: '2025-07-29' },
    { week: '周三', date: '07-30', dateStr: '2025-07-30' },
    { week: '周四', date: '07-31', dateStr: '2025-07-31' },
    { week: '周五', date: '08-01', dateStr: '2025-08-01' },
    { week: '周六', date: '08-02', dateStr: '2025-08-02' },
    { week: '周日', date: '08-03', dateStr: '2025-08-03' },
    { week: '周一', date: '08-04', dateStr: '2025-08-04' },
    { week: '周二', date: '08-05', dateStr: '2025-08-05' },
    { week: '周三', date: '08-06', dateStr: '2025-08-06' },
  ]);

  const currentTimes = ref<{ date: string; sw: { time: string; count: number }[]; xw: { time: string; count: number }[] }[]>([
    {
      date: '07-24',
      sw: [
        { time: '09:00-12:00', count: 10 },
        { time: '13:00-17:00', count: 10 },
        { time: '18:00-21:00', count: 10 },
      ],
      xw: [
        { time: '09:00-12:00', count: 10 },
        { time: '13:00-17:00', count: 10 },
        { time: '18:00-21:00', count: 10 },
      ],
    },
    { date: '07-25', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '07-26', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '07-27', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '07-28', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '07-29', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '07-30', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '07-31', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '08-01', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
    { date: '08-02', sw: [{ time: '09:00-12:00', count: 10 }], xw: [{ time: '09:00-12:00', count: 10 }] },
  ]);

  const handleSelectDate = (date, index) => {
    console.log('handleSelectDate', date, index);
    selectedDateIndex.value = index;

    // 如果有日期，设置表单日期值
    if (date) {
      // 假设年份是当前年份
      formData.yyrq = date.dateStr;
      selectedDate.value = date.dateStr;
    }
    showMoreDates.value = false;

    // 获取当前日期的时间段
    const currentDate = currentTimes.value.find((item) => item.date === date.date);
    if (currentDate) {
      selectTimesList.value = currentDate;
    }
  };

  const handleMoreSelectDate = (date, index) => {
    console.log('handleMoreSelectDate', date, index);
    selectedDateIndex.value = index;
    selectedDate.value = date.date;
    showMoreDates.value = false;
    // 获取当前日期的时间段
    const currentDate = currentTimes.value.find((item) => item.date === date.date);
    if (currentDate) {
      selectTimesList.value = currentDate;
    }
  };

  // 处理更多日期按钮点击
  const handleMoreDates = () => {
    // 打开日历选择器
    showMoreDates.value = true;
  };

  const handleChange = (e) => {
    console.log('handleChange', e);
  };

  const handleSelectTimeAM = (item, index) => {
    selectedTimePMIndex.value = null;
    selectedTimeAMIndex.value = index;
    selectedTimeSlot.value = item.time;
  };

  const handleSelectTimePM = (item, index) => {
    selectedTimeAMIndex.value = null;
    selectedTimePMIndex.value = index;
    selectedTimeSlot.value = item.time;
  };

  const handleConfirmGoods = (goods: MedicineTypes.MedicinalItem[]) => {
    console.log("🚀 ~ handleConfirmGoods ~ goods:", goods)
    selectedGoodsList.value = [...goods];
  };

  const handleSubmit = async (index) => {
    // 如果未登录，则打开登录弹窗
    if (!hasLogin.value) {
      return monkey.$stores.useAuthModalStore().open();
    }

    // 预约时间
    if (index === 1) {
      // 如果未选择日期，则打开日期选择弹窗
      console.log('🚀 ~ handleSubmit ~ selectedDate.value:', selectedDate.value);
      if (!selectedDate.value) return monkey.$helper.toast.error('请选择预约日期');

      // 如果未选择时间段，则打开时间段选择弹窗
      if (!selectedTimeSlot.value) return monkey.$helper.toast.error('请选择预约时间段');
      // 选中物品
    } else if (index === 2) {
      // 如果未选择物品，则打开物品选择弹窗
      console.log("🚀 ~ handleSubmit ~ selectedGoods:", selectedGoodsList.value)
      if (selectedGoodsList.value.length === 0) return monkey.$helper.toast.error('请选择入库物品');

      // 需判断 如果商品的斤数是0 则提示 请输入斤数 那个商品
      for (const item of selectedGoodsList.value) {
        if (item.weight === 0) return monkey.$helper.toast.error(`请输入${item.bzmc}的斤数`);
      }
    } else if (index === 3) {
      const validate = await infoRef.value.validate();
      if (!validate) return;
      const info = infoRef.value.getFormData();
      console.log("🚀 ~ handleSubmit ~ info:", info)  
    } 

    active.value = index;

    // 如果未选择物品，则打开物品选择弹窗

   
    console.log('🚀 ~ handleNextStep ~ active.value:', active.value);
  };

  const handleSubmitInbound = () => {
    if (hasLogin.value) {
    }
    console.log('handleSubmitInbound');
  };

  onLoad(() => {
    console.log('onLoad');
    if (hasLogin.value) {
      // formData.yyr = userInfo.value?.zsxm;
      // formData.yyrsfz = userInfo.value?.sfzhm;
      // formData.yyrdh = userInfo.value?.sjh;
      // formData.yyrq = monkey.$dayjs().format('YYYY-MM-DD');
    }
    handleSelectDate(currentDates.value[0], 0);
  });
 

  // 新增：判断是否为今天
  const isToday = (date) => {
    const today = new Date();
    const todayStr = `${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
    return date === todayStr;
  };
</script>

<style lang="scss" scoped></style>
