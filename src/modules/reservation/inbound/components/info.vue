<template>
  <view class="flex-1 px-4 py-2 size-full bg-white overflow-y-auto rounded-lg">
    <uni-forms ref="formRef" :model="formData" :rules="rules" label-position="top" border labelWidth="120px">
      <!-- 联系人 -->
      <uni-forms-item name="yyr" label="联系人" required>
        <uni-easyinput v-model="formData.yyr" placeholder="请输入联系人姓名" trim="both" prefixIcon="person" :inputBorder="false" />
      </uni-forms-item>

      <!-- 身份证 -->
      <uni-forms-item name="yyrsfz" label="身份证号" required>
        <uni-easyinput v-model="formData.yyrsfz" placeholder="请输入身份证号" trim="both" prefixIcon="email" :inputBorder="false" />
      </uni-forms-item>

      <!-- 联系电话 -->
      <uni-forms-item name="yyrdh" label="联系电话" required>
        <uni-easyinput v-model="formData.yyrdh" placeholder="请输入联系电话" trim="both" prefixIcon="phone" :inputBorder="false" />
      </uni-forms-item>
      <uni-forms-item name="yuany" label="备注">
        <uni-easyinput v-model="formData.yuany" type="textarea" placeholder="请输入备注信息（选填）" :maxlength="200" :autoHeight="true" class="remark-input" />
      </uni-forms-item>
    </uni-forms>
  </view>
</template>
<script setup lang="ts">
  import monkey from '@/monkey';
  // 表单数据
  const formData = reactive({
    yyr: '', // 预约人
    yyrdh: '', // 预约单号
    yyrsfz: '', // 预约身份证
    yyrq: '', // 预约日期
    yymx: [], // 预约明细
    yuany: '', // 预约原因
  });

  // 表单引用
  const formRef = ref();

  // 表单验证规则
  const rules = {
    reservationDate: {
      rules: [{ required: true, errorMessage: '请选择预约日期' }],
    },
    timeSlot: {
      rules: [{ required: true, errorMessage: '请选择预约时间段' }],
    },
    contactName: {
      rules: [
        { required: true, errorMessage: '请输入联系人姓名' },
        { minLength: 2, maxLength: 20, errorMessage: '联系人姓名长度在2-20个字符之间' },
      ],
    },
    contactPhone: {
      rules: [
        { required: true, errorMessage: '请输入联系电话' },
        { pattern: /^1[3-9]\d{9}$/, errorMessage: '请输入正确的手机号码' },
      ],
    },
  };
</script>
