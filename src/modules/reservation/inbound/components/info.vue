<template>
  <view class="flex-1 px-4 py-2 size-full bg-white overflow-y-auto rounded-lg">
    <uni-forms ref="formRef" :model="formData" :rules="rules" label-position="top" border labelWidth="120px">
      <!-- 联系人 -->
      <uni-forms-item name="yyr" label="联系人" required>
        <uni-easyinput v-model="formData.yyr" placeholder="请输入联系人姓名" trim="both" prefixIcon="person" :inputBorder="false" />
      </uni-forms-item>

      <!-- 身份证 -->
      <uni-forms-item name="yyrsfz" label="身份证号" required>
        <uni-easyinput v-model="formData.yyrsfz" placeholder="请输入身份证号" trim="both" prefixIcon="email" :inputBorder="false" />
      </uni-forms-item>

      <!-- 联系电话 -->
      <uni-forms-item name="yyrdh" label="联系电话" required>
        <uni-easyinput v-model="formData.yyrdh" placeholder="请输入联系电话" trim="both" prefixIcon="phone" :inputBorder="false" />
      </uni-forms-item>
      <uni-forms-item name="yuany" label="备注">
        <uni-easyinput v-model="formData.yuany" type="textarea" placeholder="请输入备注信息（选填）" :maxlength="200" :autoHeight="true" class="remark-input" />
      </uni-forms-item>
    </uni-forms>
  </view>
</template>
<script setup lang="ts">
  import monkey from '@/monkey';

  // 定义 emit 事件
  const emit = defineEmits<{
    'update:formData': [formData: any];
    'form-change': [formData: any];
  }>();

  // 表单数据
  const formData = reactive({
    yyr: '', // 预约人
    yyrdh: '', // 预约单号
    yyrsfz: '', // 预约身份证
    yyrq: '', // 预约日期
    yymx: [], // 预约明细
    yuany: '', // 预约原因
  });

  // 表单引用
  const formRef = ref();

  // 表单验证规则
  const rules = {
    yyr: {
      rules: [
        { required: true, errorMessage: '请输入联系人姓名' },
        { minLength: 2, maxLength: 20, errorMessage: '联系人姓名长度在2-20个字符之间' },
      ],
    },
    yyrsfz: {
      rules: [
        { required: true, errorMessage: '请输入身份证号' },
        { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, errorMessage: '请输入正确的身份证号' },
      ],
    },
    yyrdh: {
      rules: [
        { required: true, errorMessage: '请输入联系电话' },
        { pattern: /^1[3-9]\d{9}$/, errorMessage: '请输入正确的手机号码' },
      ],
    },
  };

  // 监听表单数据变化，抛出给父组件
  watch(
    formData,
    (newFormData) => {
      emit('update:formData', newFormData);
      emit('form-change', newFormData);
    },
    { deep: true }
  );

  // 表单验证方法
  const validate = async () => {
    try {
      const res = await formRef.value?.validate();
      return res;
    } catch (error) {
      console.error('表单验证失败:', error);
      return false;
    }
  };

  // 获取表单数据
  const getFormData = () => {
    return toRaw(formData);
  };

  // 重置表单
  const resetForm = () => {
    formRef.value?.clearValidate();
    Object.assign(formData, {
      yyr: '',
      yyrdh: '',
      yyrsfz: '',
      yyrq: '',
      yymx: [],
      yuany: '',
    });
  };

  // 暴露方法给父组件
  defineExpose({
    validate,
    getFormData,
    resetForm,
    formData,
  });
</script>
