<template>
  <ly-layout is-arc-bg isBackTop :active="0">
    <div class="header sticky top-0 z-20 text-white px-4 pb-2" :style="{ backgroundColor: navBarBgColor }">
      <ly-status-bar />
      <div class="flex items-center justify-between" :style="{ height: monkey.$helper.utils.getNavBarHeight() + 'px' }">
        <div class="flex items-center">
          <image class="w-28 h-[30px] rounded-lg" mode="widthFix" :src="monkey.$url.cdn(monkey.$config.about.logo)" />
        </div>
      </div>
    </div>
    <div class="flex-1 px-4 b-20 relative z-10">
      <div class="rounded-lg h-40 mb-4 shadow-lg overflow-hidden">
        <swiper class="size-full rounded-lg" :autoplay="true" :interval="3000" :circular="true" :indicator-dots="true" :indicator-color="'#fff'" :indicator-active-color="'#000'">
          <swiper-item class="size-full rounded-lg" v-for="(item, index) in swiperList" :key="index" @click="handleBannerClick(item)">
            <image class="size-full rounded-lg" :src="monkey.$url.cdn(item.url)" />
          </swiper-item>
        </swiper>
      </div>
      <div class="mb-4">
        <ui-home-points />
      </div>
      <div class="mb-4">
        <ui-home-actions />
      </div>
      <div class="mb-4">
        <ui-home-trading>
          <template #title>
            <div class="mb-4">
              <ly-title title="现货交易" more="更多" @more="handleTradingClick" />
            </div>
          </template>
        </ui-home-trading>
      </div>
      <div class="mb-4">
        <ui-home-price>
          <template #title>
            <div class="mb-4">
              <ly-title title="药材行情" more="更多" @more="handleMedicineClick" />
            </div>
          </template>
        </ui-home-price>
      </div>
      <ly-line-bar height="150"></ly-line-bar>
    </div>
  </ly-layout>
</template>

<script setup lang="ts">
  // 导入配置
  import monkey from '@/monkey';

  import type { HomeTypes } from '@/monkey/types';

  // 导航栏背景颜色
  const navBarBgColor: Ref<string> = ref('rgba(0, 109, 187, 0)');

  const swiperList = ref<HomeTypes.BannerItem[]>([]);

  /**
   * 监听页面滚动
   * @param e 滚动事件
   */
  const handlePageScroll = (e: Page.PageScrollOption) => {
    const scrollTop = e.scrollTop;
    // 设置滚动阈值，这里设为200，您可以根据需要调整
    const threshold = 200;

    if (scrollTop > threshold) {
      // 超过阈值时设置不透明背景
      navBarBgColor.value = 'rgba(62, 139, 247, 0.9)';
    } else {
      // 计算透明度
      const opacity = scrollTop / threshold;
      navBarBgColor.value = `rgba(62, 139, 247, ${opacity})`;
    }
  };

  /**
   * 交易大厅点击
   */
  const handleTradingClick = () => {
    monkey.$router.navigateTo('/modules/home/<USER>/index');
  };

  /**
   * 商品列表点击
   */
  const handleGoodsListClick = () => {
    monkey.$router.navigateTo('/modules/goods/list/index');
  };

  /**
   * 药材行情点击
   */
  const handleMedicineClick = () => {
    monkey.$router.navigateTo('/modules/home/<USER>/index');
  };

  /**
   * 药材行情点击
   */
  const handlePriceClick = () => {
    monkey.$router.navigateTo('/pages/medicine/index');
  };


  const handleBannerClick = (item: HomeTypes.BannerItem) => {
    if (item.isLink === 'shi') {
      monkey.$router.navigateTo(`/modules/home/<USER>/index?id=${item.id}&name=${item.name}`);
    } else {
      monkey.$helper.utils.previewImage(item.url);
    }
  };

  /**
   * 监听页面滚动
   * @param e 滚动事件
   */
  onPageScroll((e: Page.PageScrollOption) => {
    handlePageScroll(e);
  });

  /**
   * 获取低代码列表
   */
  const getSwiperList = () => {
    const params = monkey.$helper.param.getAuthineListParams({
      schemaCode: 'lbtgl',
      queryCondition: [
        {
          ...monkey.$helper.param.getAuthineListQueryCondition('Eq', 'tpzt', 12, '[{"key":"xs","value":"显示"}]'),
        },
      ],
    });
    monkey.$api.authine.getAuthineList(params).then((swiperRes) => {
      if (swiperRes.errcode === 0 && swiperRes.data.content) {
        // 结构item里的data
        swiperList.value = swiperRes.data.content.map((item: { data: HomeTypes.BannerData }) => ({
          id: item.data.id,
          url: item.data.tp[0].refId,
          name: item.data.tpmc,
          isLink: item.data.sftz_key,
        }));
      }
    });
  };

  onLoad(() => {
    getSwiperList();
  });
</script>
