<template>
  <view :class="[monkey.$helper.utils.hasSafeArea() ? 'fixed-footer' : 'pb-3', fiexd ? 'fixed' : 'relative']" class="bg-white bottom-0 w-full px-4 pt-3 z-10 shadow-lg">
    <div v-if="props.type === 'primary'" class="w-full text-sm bg-theme-blue text-white rounded-full h-44 flex items-center tracking-widest gap-2 justify-center active:bg-blue-700 transition-all duration-300" @click="handleSubmit">
      <text v-if="props.icon" class="text-base" :class="props.icon"></text>
      <text class="text-sm">{{ props.text }}</text>
      <text v-if="props.rightIcon" class="text-base" :class="props.rightIcon"></text>
    </div>
    <slot />
  </view>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';
  const props = withDefaults(
    defineProps<{
      text: string;
      type: 'primary' | 'secondary' | 'danger' | 'goods-detail';
      icon: string;
      rightIcon: string;
      fixed: boolean;
    }>(),
    {
      text: '提交',
      icon: 'i-mdi-check',
      rightIcon: '',
      fixed: true,
    },
  );

  const emit = defineEmits<{
    (e: 'submit'): void;
  }>();

  const handleSubmit = () => {
    emit('submit');
  };
</script>

<style lang="scss" scoped>
  .fixed-footer {
    padding-bottom: calc(env(safe-area-inset-bottom));
  }
</style>
