<template>
  <view
    :class="[
      monkey.$helper.utils.hasSafeArea() ? 'fixed-footer' : 'pb-3',
      fixed ? 'fixed' : 'relative'
    ]"
    class="bg-white bottom-0 w-full px-4 pt-3 z-10 shadow-lg border-t border-gray-100"
  >
    <!-- 单按钮模式 -->
    <block v-if="count === 1">
      <button
        :class="[
          'w-full h-12 rounded-lg flex items-center justify-center gap-2 font-medium transition-all duration-200 active:scale-95',
          getButtonClass(type),
          loading ? 'opacity-70 cursor-not-allowed' : ''
        ]"
        :disabled="disabled || loading"
        @click="handleSingleClick"
      >
        <text v-if="loading" class="i-mdi-loading animate-spin text-base"></text>
        <text v-else-if="icon" class="text-base" :class="icon"></text>
        <text class="text-sm">{{ loading ? loadingText : text }}</text>
        <text v-if="rightIcon && !loading" class="text-base" :class="rightIcon"></text>
      </button>
    </block>

    <!-- 多按钮模式 -->
    <block v-else-if="count > 1">
      <div class="flex gap-3">
        <button
          v-for="(button, index) in buttons"
          :key="index"
          :class="[
            'flex-1 h-12 rounded-lg flex items-center justify-center gap-2 font-medium transition-all duration-200 active:scale-95',
            button.type ? getButtonClass(button.type) : button.class || 'bg-gray-500 text-white hover:bg-gray-600',
            button.loading ? 'opacity-70 cursor-not-allowed' : ''
          ]"
          :disabled="button.disabled || button.loading"
          @click="handleMultiClick(button, index)"
        >
          <text v-if="button.loading" class="i-mdi-loading animate-spin text-base"></text>
          <text v-else-if="button.icon" class="text-base" :class="button.icon"></text>
          <text class="text-sm">{{ button.loading ? (button.loadingText || '处理中...') : button.text }}</text>
          <text v-if="button.rightIcon && !button.loading" class="text-base" :class="button.rightIcon"></text>
        </button>
      </div>
    </block>

    <!-- 自定义插槽 -->
    <slot />
  </view>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  // 按钮接口定义
  interface ButtonItem {
    text: string;
    icon?: string;
    rightIcon?: string;
    type?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning';
    class?: string;
    disabled?: boolean;
    loading?: boolean;
    loadingText?: string;
    click?: () => void;
  }

  // 组件属性定义
  const props = withDefaults(
    defineProps<{
      text?: string;
      type?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning';
      icon?: string;
      rightIcon?: string;
      fixed?: boolean;
      count?: number;
      disabled?: boolean;
      loading?: boolean;
      loadingText?: string;
      buttons?: ButtonItem[];
    }>(),
    {
      text: '确认',
      type: 'primary',
      icon: '',
      rightIcon: '',
      fixed: true,
      count: 1,
      disabled: false,
      loading: false,
      loadingText: '处理中...',
      buttons: () => [],
    }
  );

  // 事件定义
  const emit = defineEmits<{
    click: [];
    'button-click': [button: ButtonItem, index: number];
  }>();

  // 获取按钮样式类
  const getButtonClass = (buttonType: string) => {
    const typeMap = {
      primary: 'bg-theme-blue text-white hover:bg-blue-600 active:bg-blue-700',
      secondary: 'bg-gray-100 text-gray-700 hover:bg-gray-200 active:bg-gray-300',
      danger: 'bg-red-500 text-white hover:bg-red-600 active:bg-red-700',
      success: 'bg-green-500 text-white hover:bg-green-600 active:bg-green-700',
      warning: 'bg-yellow-500 text-white hover:bg-yellow-600 active:bg-yellow-700',
    };
    return typeMap[buttonType] || typeMap.primary;
  };

  // 单按钮点击处理
  const handleSingleClick = () => {
    if (props.disabled || props.loading) return;
    emit('click');
  };

  // 多按钮点击处理
  const handleMultiClick = (button: ButtonItem, index: number) => {
    if (button.disabled || button.loading) return;

    // 优先执行按钮自身的点击事件
    if (button.click) {
      button.click();
    }

    // 然后触发组件事件
    emit('button-click', button, index);
  };
</script>

<style lang="scss" scoped>
  .fixed-footer {
    padding-bottom: calc(env(safe-area-inset-bottom));
  }
</style>
